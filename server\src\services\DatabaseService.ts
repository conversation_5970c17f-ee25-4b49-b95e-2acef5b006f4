/**
 * Database Service for CallerRep Server
 * Handles PostgreSQL database operations using Drizzle ORM
 */

import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { eq, count, avg, sum, max, gte, desc, sql, and } from 'drizzle-orm';
import { logger } from '@/utils/logger';
import {
  devices,
  fingerprints,
  contacts,
  spamReports,
  type Device,
  type Fingerprint,
  type Contact,
  type SpamReport,
  type NewDevice,
  type NewFingerprint,
  type NewContact,
  type NewSpamReport,
} from '@/db/schema';
import { DatabaseConfig } from '@/types';

export class DatabaseService {
  private static instance: DatabaseService;
  private db: ReturnType<typeof drizzle>;
  private client: Pool;
  private config: DatabaseConfig;

  private constructor() {
    this.config = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432', 10),
      database: process.env.DB_NAME || 'callerrep',
      user: process.env.DB_USER || 'postgres',
      password: String(process.env.DB_PASSWORD || ''),
      ssl: process.env.DB_SSL === 'true',
      poolMin: parseInt(process.env.DB_POOL_MIN || '2', 10),
      poolMax: parseInt(process.env.DB_POOL_MAX || '10', 10)
    };

    // Debug logging
    logger.info(`Database config - host: ${this.config.host}, port: ${this.config.port}, database: ${this.config.database}, user: ${this.config.user}, passwordLength: ${this.config.password.length}, passwordType: ${typeof this.config.password}, ssl: ${this.config.ssl}`);

    // Create pg pool client
    this.client = new Pool({
      host: this.config.host,
      port: this.config.port,
      database: this.config.database,
      user: this.config.user,
      password: this.config.password,
      ssl: this.config.ssl,
      min: this.config.poolMin,
      max: this.config.poolMax,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 30000,
    });

    // Create Drizzle instance
    this.db = drizzle(this.client);
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public static async initialize(): Promise<void> {
    const instance = DatabaseService.getInstance();
    await instance.connect();
  }

  public static async close(): Promise<void> {
    const instance = DatabaseService.getInstance();
    await instance.disconnect();
  }

  private async connect(): Promise<void> {
    try {
      const client = await this.client.connect();
      await client.query('SELECT 1');
      client.release();
      logger.info('Database connection established');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  private async disconnect(): Promise<void> {
    try {
      await this.client.end();
      logger.info('Database connection closed');
    } catch (error) {
      logger.error('Error closing database connection:', error);
      throw error;
    }
  }

  // Fingerprint operations
  public async getFingerprintByHash(hash: string): Promise<Fingerprint | null> {
    const result = await this.db
      .select()
      .from(fingerprints)
      .where(eq(fingerprints.hash, hash))
      .limit(1);
    return result[0] || null;
  }

  public async createOrUpdateFingerprint(hash: string, contactCount: number = 1): Promise<Fingerprint> {
    const existing = await this.getFingerprintByHash(hash);

    if (existing) {
      const updated = await this.db
        .update(fingerprints)
        .set({
          contactCount,
          lastSeen: new Date(),
          updatedAt: new Date()
        })
        .where(eq(fingerprints.hash, hash))
        .returning();
      if (!updated[0]) throw new Error('Failed to update fingerprint');
      return updated[0];
    } else {
      const created = await this.db
        .insert(fingerprints)
        .values({
          hash,
          firstSeen: new Date(),
          lastSeen: new Date(),
          contactCount,
          qualityScore: 50, // Default score
          pagerankScore: '0',
          spamReports: 0,
          riskLevel: 'unknown',
        })
        .returning();
      if (!created[0]) throw new Error('Failed to create fingerprint');
      return created[0];
    }
  }

  public async updateFingerprintScores(
    hash: string,
    qualityScore: number,
    pagerankScore: number,
    riskLevel: 'low' | 'medium' | 'high' | 'unknown'
  ): Promise<void> {
    await this.db
      .update(fingerprints)
      .set({
        qualityScore,
        pagerankScore: pagerankScore.toString(),
        riskLevel,
        updatedAt: new Date()
      })
      .where(eq(fingerprints.hash, hash));
  }

  public async getAllFingerprints(): Promise<Fingerprint[]> {
    return await this.db.select().from(fingerprints);
  }

  public async getFingerprintsForPageRank(): Promise<Fingerprint[]> {
    const minContacts = parseInt(process.env.MIN_CONTACTS_FOR_SCORE || '5', 10);
    return await this.db
      .select()
      .from(fingerprints)
      .where(gte(fingerprints.contactCount, minContacts));
  }

  // Device operations
  public async getDeviceById(deviceId: string): Promise<Device | null> {
    const result = await this.db
      .select()
      .from(devices)
      .where(eq(devices.deviceId, deviceId))
      .limit(1);
    return result[0] || null;
  }

  public async createOrUpdateDevice(deviceId: string, appVersion?: string): Promise<Device> {
    const existing = await this.getDeviceById(deviceId);

    if (existing) {
      const updated = await this.db
        .update(devices)
        .set({
          lastSubmission: new Date(),
          totalSubmissions: (existing.totalSubmissions || 0) + 1,
          appVersion: appVersion || existing.appVersion,
          updatedAt: new Date()
        })
        .where(eq(devices.deviceId, deviceId))
        .returning();
      if (!updated[0]) throw new Error('Failed to update device');
      return updated[0];
    } else {
      const created = await this.db
        .insert(devices)
        .values({
          deviceId,
          firstSubmission: new Date(),
          lastSubmission: new Date(),
          totalSubmissions: 1,
          appVersion,
        })
        .returning();
      if (!created[0]) throw new Error('Failed to create device');
      return created[0];
    }
  }

  // Contact operations
  public async createContact(
    deviceId: string,
    fingerprintHash: string,
    contactTimestamp: Date,
    region?: string
  ): Promise<Contact> {
    const created = await this.db
      .insert(contacts)
      .values({
        deviceId,
        fingerprintHash,
        contactTimestamp,
        submissionTimestamp: new Date(),
        region,
      })
      .returning();
    if (!created[0]) throw new Error('Failed to create contact');
    return created[0];
  }

  public async getContactsByFingerprint(fingerprintHash: string): Promise<Contact[]> {
    return await this.db
      .select()
      .from(contacts)
      .where(eq(contacts.fingerprintHash, fingerprintHash));
  }

  // Spam report operations
  public async createSpamReport(
    fingerprintHash: string,
    deviceId: string,
    reportType: 'spam' | 'scam' | 'robocall' | 'telemarketer' | 'other',
    confidence: number,
    description?: string
  ): Promise<SpamReport> {
    const created = await this.db
      .insert(spamReports)
      .values({
        fingerprintHash,
        deviceId,
        reportType,
        confidence: confidence.toString(),
        description,
      })
      .returning();

    // Update spam report count on fingerprint
    await this.db
      .update(fingerprints)
      .set({
        spamReports: sql`${fingerprints.spamReports} + 1`
      })
      .where(eq(fingerprints.hash, fingerprintHash));

    if (!created[0]) throw new Error('Failed to create spam report');
    return created[0];
  }

  // Anti-abuse methods for spam reports
  public async getSpamReportByDeviceAndFingerprint(
    deviceId: string,
    fingerprintHash: string
  ): Promise<SpamReport | null> {
    const reports = await this.db
      .select()
      .from(spamReports)
      .where(
        and(
          eq(spamReports.deviceId, deviceId),
          eq(spamReports.fingerprintHash, fingerprintHash)
        )
      )
      .limit(1);

    return reports[0] || null;
  }

  public async getRecentSpamReportsByDevice(
    deviceId: string,
    hoursBack: number
  ): Promise<SpamReport[]> {
    const cutoffTime = new Date(Date.now() - (hoursBack * 60 * 60 * 1000));

    return await this.db
      .select()
      .from(spamReports)
      .where(
        and(
          eq(spamReports.deviceId, deviceId),
          gte(spamReports.createdAt, cutoffTime)
        )
      );
  }

  public async getSpamReportsByFingerprint(
    fingerprintHash: string
  ): Promise<SpamReport[]> {
    return await this.db
      .select()
      .from(spamReports)
      .where(eq(spamReports.fingerprintHash, fingerprintHash));
  }

  public async getDeviceById(deviceId: string): Promise<Device | null> {
    const devices_result = await this.db
      .select()
      .from(devices)
      .where(eq(devices.deviceId, deviceId))
      .limit(1);

    return devices_result[0] || null;
  }

  public async getSpamReportsByFingerprint(fingerprintHash: string): Promise<SpamReport[]> {
    return await this.db
      .select()
      .from(spamReports)
      .where(eq(spamReports.fingerprintHash, fingerprintHash));
  }

  // Analytics operations
  public async getAnalyticsData(): Promise<any> {
    const [
      fingerprintCount,
      deviceCount,
      submissionCount,
      avgQualityScore,
      spamReportCount
    ] = await Promise.all([
      this.db.select({ count: count() }).from(fingerprints),
      this.db.select({ count: count() }).from(devices),
      this.db.select({ count: count() }).from(contacts),
      this.db.select({ avg: avg(fingerprints.qualityScore) }).from(fingerprints),
      this.db.select({ count: count() }).from(spamReports)
    ]);

    return {
      totalFingerprints: fingerprintCount[0]?.count || 0,
      totalDevices: deviceCount[0]?.count || 0,
      totalSubmissions: submissionCount[0]?.count || 0,
      averageQualityScore: parseFloat(avgQualityScore[0]?.avg || '0'),
      spamReports: spamReportCount[0]?.count || 0
    };
  }

  // Health check
  public async healthCheck(): Promise<boolean> {
    try {
      const client = await this.client.connect();
      await client.query('SELECT 1');
      client.release();
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  // Get database instance for complex queries
  public getDb(): ReturnType<typeof drizzle> {
    return this.db;
  }

  // Get client for raw queries
  public getClient(): Pool {
    return this.client;
  }
}
