/**
 * Fingerprint submission routes for CallerRep Server
 */

import { Router } from 'express';
import <PERSON><PERSON> from 'joi';
import { asyncHandler, createValidationError } from '@/middleware/errorHandler';
import { submissionRateLimiter, strictRateLimiter, spamReportRateLimiter } from '@/middleware/rateLimiter';
import { optionalUserAuth } from '@/middleware/auth';
import { DatabaseService } from '@/services/DatabaseService';
import { CacheService } from '@/services/CacheService';
import { SubscriptionService } from '@/services/SubscriptionService';
import { logger } from '@/utils/logger';
import {
  FingerprintSubmission,
  PhoneFingerprint,
  SubmissionResponse,
  SpamReport,
  ReportType
} from '@/types';
import {
  defaultAntiAbuseConfig,
  calculateDeviceTrustScore,
  calculateSpamPenalty,
  detectSuspiciousPatterns
} from '@/config/antiAbuse';

const router = Router();

// Get service instances
const db = DatabaseService.getInstance();
const cache = CacheService.getInstance();
const subscriptionService = SubscriptionService.getInstance();

// Decay factor for contact scores (24 hours = 1 day)
const DECAY_FACTOR_PER_DAY = 0.95; // 5% decay per day
const HOURS_PER_DAY = 24;

/**
 * Get the maximum contact count from the database for dynamic scaling
 * @param client - Database client
 * @returns The highest contact_count value in the fingerprints table
 */
async function getMaxContactCount(client: any): Promise<number> {
  try {
    const result = await client.query(
      'SELECT MAX(contact_count) as max_count FROM fingerprints'
    );

    const maxCount = result.rows[0]?.max_count || 1;
    return Math.max(1, maxCount); // Ensure minimum of 1
  } catch (error) {
    logger.error('Error getting max contact count:', error);
    return 1; // Fallback to 1 if query fails
  }
}

/**
 * Calculate normalized quality score from contact count using dynamic scaling
 * Scales relative to the most popular contact in the system (highest contact_count)
 *
 * @param contactCount - The raw contact count (with decay applied)
 * @param maxContactCount - The highest contact count in the system (for scaling)
 * @returns Quality score between 25-100 that scales relative to the most popular contact
 */
function calculateNormalizedQualityScore(contactCount: number, maxContactCount: number = 1): number {
  const base = 25;           // Minimum score (unknown contacts)
  const maxScore = 100;      // Maximum score (most popular contact)
  const scoreRange = maxScore - base; // 75 points to distribute

  // Use logarithmic scaling for natural distribution
  // Most popular contact gets close to 100, others scale logarithmically
  const logContactCount = Math.log(contactCount + 1);
  const logMaxCount = Math.log(maxContactCount + 1);

  // Calculate relative position (0 to 1)
  const relativePosition = logContactCount / logMaxCount;

  // Scale to our score range
  const scaledScore = base + (scoreRange * relativePosition);

  // Add consistent randomization based on contact count for natural variation
  const seed = Math.sin(contactCount * 12.9898) * 43758.5453;
  const randomOffset = (seed - Math.floor(seed)) * 4 - 2; // ±2 points variation

  // Apply bounds and round to integer
  const finalScore = Math.round(Math.min(maxScore, Math.max(base, scaledScore + randomOffset)));

  return finalScore;
}

/**
 * Increment fingerprint score with decay-based system
 * Privacy-preserving: no device tracking, just score increments with natural decay
 */
async function incrementFingerprintScore(fingerprintHash: string, region?: string): Promise<void> {
  const now = new Date();

  // Get existing fingerprint or create new one
  const existing = await db.getFingerprintByHash(fingerprintHash);

  // Get the current maximum contact count for dynamic scaling
  const client = await db.getClient().connect();
  let maxContactCount: number;
  try {
    maxContactCount = await getMaxContactCount(client);
  } finally {
    client.release();
  }

  if (existing) {
    // Apply decay based on time since last update
    const timeSinceUpdate = now.getTime() - existing.updatedAt.getTime();
    const daysSinceUpdate = timeSinceUpdate / (1000 * 60 * 60 * 24);

    // Decay rate: lose 1% per day (configurable)
    const decayRate = parseFloat(process.env.CONTACT_DECAY_RATE || '0.01');
    const decayFactor = Math.pow(1 - decayRate, daysSinceUpdate);

    // Apply decay to current score, then increment
    const decayedScore = existing.contactCount * decayFactor;
    const newScore = decayedScore + 1; // Increment by 1 for this submission

    // Calculate quality score using dynamic scaling relative to most popular contact
    const qualityScore = calculateNormalizedQualityScore(newScore, maxContactCount);

    logger.debug(`📉 Fingerprint ${fingerprintHash.substring(0, 8)}...: ${existing.contactCount} → ${decayedScore.toFixed(2)} (decay) → ${newScore.toFixed(2)} (contact_count) → ${qualityScore} (quality_score) [max: ${maxContactCount}]`);

    // Update with new score and quality score (round to integer for database storage)
    const updateClient = await db.getClient().connect();
    try {
      await updateClient.query(
        'UPDATE fingerprints SET contact_count = $1, quality_score = $2, last_seen = $3, updated_at = $4 WHERE hash = $5',
        [Math.round(newScore), qualityScore, now, now, fingerprintHash]
      );
    } finally {
      updateClient.release();
    }
  } else {
    // New fingerprint: start with contact count of 1 and normalized quality score
    const initialQualityScore = calculateNormalizedQualityScore(1, maxContactCount);
    const insertClient = await db.getClient().connect();
    try {
      await insertClient.query(
        'INSERT INTO fingerprints (hash, first_seen, last_seen, contact_count, quality_score, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7)',
        [fingerprintHash, now, now, 1, initialQualityScore, now, now]
      );
    } finally {
      insertClient.release();
    }

    logger.debug(`🆕 New fingerprint ${fingerprintHash.substring(0, 8)}...: contact_count = 1, quality_score = ${initialQualityScore} [max: ${maxContactCount}]`);
  }
}

// Validation schemas
const phoneFingerprintSchema = Joi.object({
  hash: Joi.string().length(64).hex().required()
    .messages({
      'string.length': 'Fingerprint hash must be exactly 64 characters',
      'string.hex': 'Fingerprint hash must be a valid hexadecimal string',
    }),
  timestamp: Joi.number().integer().min(0).required()
    .messages({
      'number.min': 'Timestamp must be a positive number',
    }),
  region: Joi.string().length(2).optional()
    .messages({
      'string.length': 'Region must be a 2-character country code',
    }),
});

const submissionSchema = Joi.object({
  deviceId: Joi.string().min(10).max(100).required()
    .messages({
      'string.min': 'Device ID must be at least 10 characters',
      'string.max': 'Device ID must be at most 100 characters',
    }),
  fingerprints: Joi.array()
    .items(phoneFingerprintSchema)
    .min(1)
    .max(parseInt(process.env.MAX_BATCH_SIZE || '1000', 10))
    .required()
    .messages({
      'array.min': 'At least one fingerprint is required',
      'array.max': `Maximum ${process.env.MAX_BATCH_SIZE || '1000'} fingerprints allowed`,
    }),
  submissionTime: Joi.number().integer().min(0).required(),
  appVersion: Joi.string().max(20).optional(),
  isFullContactList: Joi.boolean().optional().default(false),
});

const spamReportSchema = Joi.object({
  fingerprint: Joi.string().length(64).hex().required(),
  deviceId: Joi.string().min(10).max(100).required(),
  reportType: Joi.string().valid('spam', 'scam', 'robocall', 'telemarketer', 'other').required(),
  confidence: Joi.number().min(0).max(1).required(),
  description: Joi.string().max(500).optional(),
});

/**
 * POST /api/v1/fingerprint/submit
 * Submit contact fingerprints from a device
 */
router.post('/submit', submissionRateLimiter, optionalUserAuth, asyncHandler(async (req, res) => {
  const { error, value } = submissionSchema.validate(req.body);
  if (error) {
    throw createValidationError(error.details[0].message);
  }

  const submission: FingerprintSubmission = value;

  let processed = 0;
  let errors = 0;

  try {
    // Create or update device record for analytics only (no contact tracking)
    await db.createOrUpdateDevice(submission.deviceId, submission.appVersion);

    // Process each fingerprint with decay-based scoring
    for (const fingerprint of submission.fingerprints) {
      try {
        // Privacy-preserving decay-based scoring
        await incrementFingerprintScore(fingerprint.hash, fingerprint.region);
        processed++;

        // Clear cache for this fingerprint to force recalculation
        try {
          await cache.del(`quality:${fingerprint.hash}`);
        } catch (cacheError) {
          // Cache errors are non-critical
          logger.debug(`Cache clear failed for ${fingerprint.hash}:`, cacheError);
        }

      } catch (fingerprintError) {
        logger.error(`Error processing fingerprint ${fingerprint.hash}:`, fingerprintError);
        errors++;
      }
    }

    // Update device fingerprint count
    const client = await db.getClient().connect();
    try {
      await client.query(
        'UPDATE devices SET total_fingerprints = total_fingerprints + $1 WHERE device_id = $2',
        [processed, submission.deviceId]
      );
    } finally {
      client.release();
    }

    const response: SubmissionResponse = {
      success: true,
      processed,
      duplicates: 0, // No duplicates in decay system - all submissions increment scores
      errors,
      message: `Successfully processed ${processed} fingerprints with decay-based scoring`,
    };

    logger.info(`📊 Decay-based fingerprint submission processed`, {
      deviceId: submission.deviceId.substring(0, 8) + '...',
      total: submission.fingerprints.length,
      processed,
      errors,
      appVersion: submission.appVersion,
      isFullContactList: submission.isFullContactList,
    });

    // Track usage for authenticated users
    if (req.user?.id) {
      await subscriptionService.trackUsage(req.user.id, submission.deviceId, 'fingerprint_submission');
    }

    res.json(response);

  } catch (error) {
    logger.error('Error processing fingerprint submission:', error);
    
    const response: SubmissionResponse = {
      success: false,
      processed,
      duplicates: 0,
      errors: errors + 1,
      message: 'Failed to process submission',
    };

    res.status(500).json(response);
  }
}));

/**
 * POST /api/v1/fingerprint/report-spam
 * Report a phone number as spam
 */
router.post('/report-spam', spamReportRateLimiter, optionalUserAuth, asyncHandler(async (req, res) => {
  const { error, value } = spamReportSchema.validate(req.body);
  if (error) {
    throw createValidationError(error.details[0].message);
  }

  const report: SpamReport = {
    ...value,
    timestamp: Date.now(),
  };

  const db = DatabaseService.getInstance();
  const cache = CacheService.getInstance();

  // Anti-abuse checks

  // 1. Check for duplicate reports from same device for same fingerprint
  const existingReport = await db.getSpamReportByDeviceAndFingerprint(report.deviceId, report.fingerprint);
  if (existingReport) {
    return res.status(409).json({
      success: false,
      message: 'You have already reported this number',
    });
  }

  // 2. Check device report frequency (prevent mass reporting)
  const recentReports = await db.getRecentSpamReportsByDevice(report.deviceId, 24); // Last 24 hours
  if (recentReports.length >= defaultAntiAbuseConfig.maxReportsPerDay) {
    return res.status(429).json({
      success: false,
      message: 'Daily report limit exceeded. Please try again tomorrow.',
    });
  }

  // 3. Get device trust score
  const device = await db.getDeviceById(report.deviceId);
  const deviceTrustScore = device
    ? calculateDeviceTrustScore(device.firstSubmission, device.totalSubmissions || 0)
    : defaultAntiAbuseConfig.newDevicePenaltyMultiplier;

  // 4. Validate confidence level (low confidence reports have less impact)
  const adjustedConfidence = Math.max(
    defaultAntiAbuseConfig.minConfidenceLevel,
    Math.min(defaultAntiAbuseConfig.maxConfidenceLevel, report.confidence)
  );

  // Create spam report
  await db.createSpamReport(
    report.fingerprint,
    report.deviceId,
    report.reportType,
    adjustedConfidence,
    report.description
  );

  // Get updated fingerprint data
  const fingerprint = await db.getFingerprintByHash(report.fingerprint);

  if (fingerprint) {
    // Get all reports for this fingerprint
    const allReports = await db.getSpamReportsByFingerprint(report.fingerprint);

    // Add device trust scores to reports
    const reportsWithTrust = await Promise.all(
      allReports.map(async (r) => {
        const reportDevice = await db.getDeviceById(r.deviceId);
        const trust = reportDevice
          ? calculateDeviceTrustScore(reportDevice.firstSubmission, reportDevice.totalSubmissions || 0)
          : defaultAntiAbuseConfig.newDevicePenaltyMultiplier;
        return { ...r, deviceTrust: trust };
      })
    );

    // Calculate confidence and trust-weighted spam penalty
    const spamPenalty = calculateSpamPenalty(reportsWithTrust);
    const newQualityScore = Math.max(0, fingerprint.quality_score - spamPenalty);

    // Detect suspicious patterns
    const suspiciousAnalysis = detectSuspiciousPatterns(allReports);
    if (suspiciousAnalysis.suspicious) {
      logger.warn(`Suspicious spam reporting pattern detected for ${report.fingerprint.substring(0, 8)}...`, {
        reasons: suspiciousAnalysis.reasons,
        reportCount: allReports.length
      });
    }
    
    // Update risk level if necessary
    let newRiskLevel = fingerprint.risk_level;
    if (fingerprint.spam_reports >= parseInt(process.env.SPAM_REPORT_THRESHOLD || '10', 10)) {
      newRiskLevel = 'high';
    } else if (newQualityScore < 40) {
      newRiskLevel = 'high';
    } else if (newQualityScore < 60) {
      newRiskLevel = 'medium';
    }

    // Update database
    await db.updateFingerprintScores(
      report.fingerprint,
      newQualityScore,
      fingerprint.pagerank_score,
      newRiskLevel
    );

    // Clear cache to force fresh calculation
    await cache.del(`quality:${report.fingerprint}`);
  }

  logger.info(`Spam report submitted`, {
    fingerprint: report.fingerprint.substring(0, 8) + '...',
    reportType: report.reportType,
    confidence: report.confidence,
    deviceId: report.deviceId.substring(0, 8) + '...',
  });

  // Track usage for authenticated users
  if (req.user?.id) {
    await subscriptionService.trackUsage(req.user.id, report.deviceId, 'spam_report');
  }

  res.json({
    success: true,
    message: 'Spam report submitted successfully',
  });
}));

/**
 * GET /api/v1/fingerprint/stats
 * Get fingerprint submission statistics
 */
router.get('/stats', asyncHandler(async (req, res) => {
  const db = DatabaseService.getInstance();
  
  const client = await db.getClient().connect();
  try {
    const [deviceStats, contactStats, reportStats] = await Promise.all([
      client.query(`
        SELECT
          COUNT(*) as total_devices,
          SUM(total_submissions) as total_submissions,
          SUM(total_fingerprints) as total_fingerprints,
          AVG(total_fingerprints) as avg_fingerprints_per_device
        FROM devices
      `),

      client.query(`
        SELECT
          COUNT(*) as total_contacts,
          COUNT(DISTINCT fingerprint_hash) as unique_fingerprints,
          MAX(created_at) as last_submission
        FROM contacts
      `),

      client.query(`
        SELECT
          COUNT(*) as total_reports,
          COUNT(DISTINCT fingerprint_hash) as reported_fingerprints
        FROM spam_reports
      `),
    ]);

    const response = {
      devices: {
        total: parseInt(deviceStats.rows[0].total_devices, 10),
        totalSubmissions: parseInt(deviceStats.rows[0].total_submissions, 10),
        totalFingerprints: parseInt(deviceStats.rows[0].total_fingerprints, 10),
        avgFingerprintsPerDevice: parseFloat(deviceStats.rows[0].avg_fingerprints_per_device) || 0,
      },
      contacts: {
        total: parseInt(contactStats.rows[0].total_contacts, 10),
        uniqueFingerprints: parseInt(contactStats.rows[0].unique_fingerprints, 10),
        lastSubmission: contactStats.rows[0].last_submission,
      },
      spamReports: {
        total: parseInt(reportStats.rows[0].total_reports, 10),
        reportedFingerprints: parseInt(reportStats.rows[0].reported_fingerprints, 10),
      },
    };

    res.json(response);
  } finally {
    client.release();
  }
}));

export default router;
