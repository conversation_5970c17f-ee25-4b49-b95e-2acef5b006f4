/**
 * Rate limiting middleware for CallerRep Server
 */

import { Request, Response, NextFunction } from 'express';
import { CacheService } from '@/services/CacheService';
import { createRateLimitError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

interface RateLimitConfig {
  windowMs: number;    // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (req: Request) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export class RateLimiter {
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = {
      keyGenerator: (req) => req.ip,
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      ...config,
    };
  }

  public middleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const cache = CacheService.getInstance();
        
        // Skip if cache is not available
        if (!cache.isReady()) {
          logger.warn('Rate limiter: Cache not available, allowing request');
          return next();
        }

        const key = this.config.keyGenerator!(req);
        const identifier = `ratelimit:${key}`;
        const windowSeconds = Math.ceil(this.config.windowMs / 1000);

        const result = await cache.checkRateLimit(
          identifier,
          this.config.maxRequests,
          windowSeconds
        );

        // Add rate limit info to request
        req.rateLimitInfo = {
          limit: this.config.maxRequests,
          remaining: result.remaining,
          reset: result.resetTime,
        };

        // Add headers
        res.set({
          'X-RateLimit-Limit': this.config.maxRequests.toString(),
          'X-RateLimit-Remaining': result.remaining.toString(),
          'X-RateLimit-Reset': Math.ceil(result.resetTime.getTime() / 1000).toString(),
        });

        if (!result.allowed) {
          logger.warn(`Rate limit exceeded for ${key}`, {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            url: req.url,
            method: req.method,
          });
          
          throw createRateLimitError();
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  }
}

// Default rate limiter for general API usage
export const rateLimiter = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: parseInt(process.env.API_RATE_LIMIT || '1000', 10),
  keyGenerator: (req) => {
    // Use API key if available, otherwise IP
    return req.apiKey || req.ip;
  },
}).middleware();

// Strict rate limiter for sensitive operations
export const strictRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 10,
  keyGenerator: (req) => req.ip,
}).middleware();

// Rate limiter for fingerprint submissions
export const submissionRateLimiter = new RateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxRequests: 100,
  keyGenerator: (req) => {
    // Use device ID from request body if available
    const deviceId = req.body?.deviceId;
    return deviceId || req.ip;
  },
}).middleware();

// Rate limiter for quality score requests
export const qualityRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100,
  keyGenerator: (req) => req.apiKey || req.ip,
}).middleware();

// Rate limiter for spam reports - more lenient than strict rate limiter
export const spamReportRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 50, // Allow 50 spam reports per minute per device
  keyGenerator: (req) => {
    // Use device ID from request body if available, otherwise IP
    const deviceId = req.body?.deviceId;
    return deviceId || req.ip;
  },
}).middleware();

// Simple rate limiter factory function for auth routes
export const createRateLimiter = (options: {
  windowMs: number;
  max: number;
  message?: string;
}) => {
  return new RateLimiter({
    windowMs: options.windowMs,
    maxRequests: options.max,
    keyGenerator: (req) => req.ip,
  }).middleware();
};

export default rateLimiter;
