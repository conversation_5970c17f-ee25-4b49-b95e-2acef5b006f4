#!/usr/bin/env node

/**
 * Simple script to create a test invoice without complex dependencies
 */

const { Pool } = require('pg');
const Stripe = require('stripe');
require('dotenv').config();

// Database connection
const db = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'callerrep',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
});

// Stripe client
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

async function createTestInvoice() {
  try {
    const deviceFingerprint = process.argv[2] || 'test-device-' + Date.now();
    const billableRequests = parseInt(process.argv[3]) || 150;
    
    console.log('📧 Creating test invoice...');
    console.log(`Device Fingerprint: ${deviceFingerprint}`);
    console.log(`Billable Requests: ${billableRequests}`);
    
    // 1. Create device usage record for previous month
    const previousMonth = new Date();
    previousMonth.setMonth(previousMonth.getMonth() - 1);
    previousMonth.setDate(1);
    previousMonth.setHours(0, 0, 0, 0);
    const billingMonth = previousMonth.toISOString().split('T')[0];
    
    console.log(`📅 Billing Month: ${billingMonth}`);
    
    // Insert or update usage record
    await db.query(`
      INSERT INTO device_usage (device_fingerprint, billing_month, current_month_requests, total_billed_requests, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (device_fingerprint)
      DO UPDATE SET
        billing_month = $2,
        current_month_requests = $3,
        total_billed_requests = $4,
        updated_at = $6
    `, [deviceFingerprint, previousMonth, billableRequests, billableRequests, new Date(), new Date()]);
    
    console.log('✅ Device usage record created/updated');
    
    // 2. Calculate bill amount
    const freeRequestsPerMonth = parseInt(process.env.FREE_REQUESTS_PER_MONTH) || 100;
    const costPerRequestCents = parseInt(process.env.COST_PER_REQUEST_CENTS) || 1;
    const minimumBillAmountCents = parseInt(process.env.MINIMUM_BILL_AMOUNT_CENTS) || 100;
    
    const billableRequestsCount = Math.max(0, billableRequests - freeRequestsPerMonth);
    let amountCents = billableRequestsCount * costPerRequestCents;
    
    // Apply minimum bill amount
    if (amountCents > 0 && amountCents < minimumBillAmountCents) {
      amountCents = minimumBillAmountCents;
    }
    
    if (amountCents <= 0) {
      console.log('💰 No bill needed - usage is within free tier');
      return;
    }
    
    console.log(`💰 Bill Amount: $${(amountCents / 100).toFixed(2)} (${billableRequestsCount} billable requests)`);
    
    // 3. Create bill record
    const dueDate = new Date(previousMonth);
    dueDate.setMonth(dueDate.getMonth() + 1);
    dueDate.setDate(15); // Due on 15th of next month
    
    const billResult = await db.query(`
      INSERT INTO device_bills (device_fingerprint, billing_month, free_requests, billable_requests, amount_cents, status, due_date, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, 'pending', $6, $7, $8)
      RETURNING *
    `, [deviceFingerprint, previousMonth, freeRequestsPerMonth, billableRequestsCount, amountCents, dueDate, new Date(), new Date()]);
    
    const bill = billResult.rows[0];
    console.log('📄 Bill created:', {
      id: bill.id,
      amount: `$${(bill.amount_cents / 100).toFixed(2)}`,
      status: bill.status,
      dueDate: bill.due_date
    });
    
    // 4. Create Stripe invoice (if Stripe is configured)
    if (process.env.STRIPE_SECRET_KEY && process.env.STRIPE_SECRET_KEY !== 'your_stripe_secret_key_here') {
      try {
        console.log('💳 Creating Stripe invoice...');
        
        // Create or retrieve customer
        const customers = await stripe.customers.list({
          email: `device-${deviceFingerprint.substring(0, 8)}@callerrep.app`,
          limit: 1
        });
        
        let customer;
        if (customers.data.length > 0) {
          customer = customers.data[0];
        } else {
          customer = await stripe.customers.create({
            email: `device-${deviceFingerprint.substring(0, 8)}@callerrep.app`,
            description: `Device: ${deviceFingerprint}`,
            metadata: {
              deviceFingerprint: deviceFingerprint,
              billId: bill.id.toString()
            }
          });
        }
        
        // Create invoice
        const invoice = await stripe.invoices.create({
          customer: customer.id,
          description: `ContactRanking API Usage - ${billingMonth}`,
          metadata: {
            deviceFingerprint: deviceFingerprint,
            billId: bill.id.toString(),
            billingMonth: billingMonth
          },
          auto_advance: false, // Don't auto-finalize
        });
        
        // Add line item
        await stripe.invoiceItems.create({
          customer: customer.id,
          invoice: invoice.id,
          amount: amountCents,
          currency: 'usd',
          description: `API Requests (${billableRequestsCount} billable requests)`,
          metadata: {
            deviceFingerprint: deviceFingerprint,
            billId: bill.id.toString()
          }
        });
        
        // Finalize the invoice
        const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);
        
        // Update bill with Stripe invoice ID
        await db.query(`
          UPDATE device_bills
          SET stripe_invoice_id = $1, updated_at = $2
          WHERE id = $3
        `, [finalizedInvoice.id, new Date(), bill.id]);
        
        console.log('✅ Stripe invoice created successfully!');
        console.log('💳 Stripe Invoice Details:');
        console.log(`   - Invoice ID: ${finalizedInvoice.id}`);
        console.log(`   - Hosted URL: ${finalizedInvoice.hosted_invoice_url}`);
        console.log(`   - Amount: $${(finalizedInvoice.amount_due / 100).toFixed(2)}`);
        console.log(`   - Status: ${finalizedInvoice.status}`);
        
        console.log('\n📱 To test in the mobile app:');
        console.log(`   1. Use device fingerprint: ${deviceFingerprint}`);
        console.log(`   2. Check the Billing screen for the new bill`);
        console.log(`   3. Try the "Pay Now" button to open: ${finalizedInvoice.hosted_invoice_url}`);
        
      } catch (stripeError) {
        console.warn('⚠️ Failed to create Stripe invoice:', stripeError.message);
        console.log('📄 Bill was still created in database');
      }
    } else {
      console.log('⚠️ Stripe not configured - bill created without payment link');
      console.log('💡 Set STRIPE_SECRET_KEY in .env to enable Stripe integration');
    }
    
    console.log('\n✅ Test invoice creation completed!');
    
  } catch (error) {
    console.error('❌ Error creating test invoice:', error);
    process.exit(1);
  } finally {
    await db.end();
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Usage: node scripts/simple-test-invoice.js [deviceFingerprint] [billableRequests]');
  console.log('');
  console.log('Arguments:');
  console.log('  deviceFingerprint  - Unique device identifier (default: auto-generated)');
  console.log('  billableRequests   - Number of billable requests (default: 150)');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/simple-test-invoice.js');
  console.log('  node scripts/simple-test-invoice.js my-test-device 200');
  console.log('  node scripts/simple-test-invoice.js test-device-123 75');
  process.exit(0);
}

createTestInvoice();
