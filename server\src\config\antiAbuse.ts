/**
 * Anti-abuse configuration for spam reporting system
 */

export interface AntiAbuseConfig {
  // Rate limiting
  maxReportsPerDay: number;
  maxReportsPerHour: number;
  
  // Duplicate prevention
  preventDuplicateReports: boolean;
  
  // Confidence weighting
  minConfidenceLevel: number;
  maxConfidenceLevel: number;
  
  // Spam penalty calculation
  maxSpamPenalty: number;
  baseReportPenalty: number;
  
  // Device trust scoring
  enableDeviceTrustScoring: boolean;
  newDevicePenaltyMultiplier: number;
  trustedDeviceThreshold: number; // Days since first submission
  
  // Report validation
  requireDescription: boolean;
  minDescriptionLength: number;
  maxDescriptionLength: number;
  
  // Automatic review thresholds
  autoReviewThreshold: number; // Number of reports that trigger review
  suspiciousPatternDetection: boolean;
}

export const defaultAntiAbuseConfig: AntiAbuseConfig = {
  // Rate limiting
  maxReportsPerDay: 20,
  maxReportsPerHour: 5,
  
  // Duplicate prevention
  preventDuplicateReports: true,
  
  // Confidence weighting
  minConfidenceLevel: 0.1,
  maxConfidenceLevel: 1.0,
  
  // Spam penalty calculation
  maxSpamPenalty: 50,
  baseReportPenalty: 5,
  
  // Device trust scoring
  enableDeviceTrustScoring: true,
  newDevicePenaltyMultiplier: 0.5, // New devices have 50% impact
  trustedDeviceThreshold: 30, // 30 days to become trusted
  
  // Report validation
  requireDescription: false,
  minDescriptionLength: 10,
  maxDescriptionLength: 500,
  
  // Automatic review thresholds
  autoReviewThreshold: 15, // Review numbers with 15+ reports
  suspiciousPatternDetection: true,
};

/**
 * Calculate device trust score based on submission history
 */
export function calculateDeviceTrustScore(
  firstSubmissionDate: Date,
  totalSubmissions: number,
  config: AntiAbuseConfig = defaultAntiAbuseConfig
): number {
  if (!config.enableDeviceTrustScoring) {
    return 1.0;
  }
  
  const daysSinceFirst = (Date.now() - firstSubmissionDate.getTime()) / (1000 * 60 * 60 * 24);
  
  // Base trust from age
  const ageTrust = Math.min(daysSinceFirst / config.trustedDeviceThreshold, 1.0);
  
  // Base trust from submission count (more submissions = more trust)
  const submissionTrust = Math.min(totalSubmissions / 50, 1.0);
  
  // Combined trust score (weighted average)
  return (ageTrust * 0.7) + (submissionTrust * 0.3);
}

/**
 * Calculate confidence-weighted spam penalty
 */
export function calculateSpamPenalty(
  reports: Array<{ confidence: string; deviceTrust?: number }>,
  config: AntiAbuseConfig = defaultAntiAbuseConfig
): number {
  const weightedScore = reports.reduce((total, report) => {
    const confidence = Math.max(
      config.minConfidenceLevel,
      Math.min(config.maxConfidenceLevel, parseFloat(report.confidence || '0.5'))
    );
    
    const deviceTrust = report.deviceTrust || 1.0;
    const reportWeight = confidence * deviceTrust * config.baseReportPenalty;
    
    return total + reportWeight;
  }, 0);
  
  return Math.min(weightedScore, config.maxSpamPenalty);
}

/**
 * Detect suspicious reporting patterns
 */
export function detectSuspiciousPatterns(
  reports: Array<{
    deviceId: string;
    createdAt: Date;
    confidence: string;
    reportType: string;
  }>
): {
  suspicious: boolean;
  reasons: string[];
} {
  const reasons: string[] = [];
  
  // Check for mass reporting from single device
  const deviceCounts = new Map<string, number>();
  reports.forEach(report => {
    deviceCounts.set(report.deviceId, (deviceCounts.get(report.deviceId) || 0) + 1);
  });
  
  const maxReportsFromSingleDevice = Math.max(...deviceCounts.values());
  if (maxReportsFromSingleDevice > 5) {
    reasons.push(`Single device submitted ${maxReportsFromSingleDevice} reports`);
  }
  
  // Check for reports in rapid succession
  const sortedReports = reports.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
  let rapidReports = 0;
  for (let i = 1; i < sortedReports.length; i++) {
    const timeDiff = sortedReports[i].createdAt.getTime() - sortedReports[i-1].createdAt.getTime();
    if (timeDiff < 60000) { // Less than 1 minute apart
      rapidReports++;
    }
  }
  
  if (rapidReports > 3) {
    reasons.push(`${rapidReports} reports submitted within 1 minute of each other`);
  }
  
  // Check for identical confidence levels (possible bot)
  const confidenceLevels = reports.map(r => r.confidence);
  const uniqueConfidences = new Set(confidenceLevels);
  if (reports.length > 5 && uniqueConfidences.size === 1) {
    reasons.push('All reports have identical confidence levels');
  }
  
  return {
    suspicious: reasons.length > 0,
    reasons
  };
}
