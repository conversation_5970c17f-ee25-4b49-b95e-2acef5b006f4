{"info": {"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, "cxxBuildFolder": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z91dn29\\armeabi-v7a", "soFolder": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\4z91dn29\\obj\\armeabi-v7a", "soRepublishFolder": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cmake\\debug\\obj\\armeabi-v7a", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared", "-DRNS_NEW_ARCH_ENABLED=false", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\.cxx", "intermediatesBaseFolder": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\build\\intermediates", "intermediatesFolder": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx", "gradleModulePathName": ":react-native-screens", "moduleRootFolder": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android", "moduleBuildFile": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\build.gradle", "makeFile": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "F:\\Android\\Sdk\\ndk\\27.1.12297006", "ndkFolderBeforeSymLinking": "F:\\Android\\Sdk\\ndk\\27.1.12297006", "ndkVersion": "27.1.12297006", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "F:\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "F:\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "F:\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "F:\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "F:\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "F:\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\android", "sdkFolder": "F:\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar"], "prefabPackages": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5d0a6fceadea979a29a63bd98787fde9\\transformed\\react-android-0.80.2-debug\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\51dbd9ae21c085b2cb843db84b5d6696\\transformed\\fbjni-0.7.0\\prefab"], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z91dn29\\prefab\\armeabi-v7a", "isActiveAbi": true, "fullConfigurationHash": "4z91dn291a5b521j271r2jm6h316r62414k4d66566162114p123l1x623g2c", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.9.2.\n#   - $NDK is the path to NDK 27.1.12297006.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HC:/Users/<USER>/source/repos/contactRanking/app/node_modules/react-native-screens/android\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-<PERSON><PERSON><PERSON>_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:/Users/<USER>/source/repos/contactRanking/app/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:/Users/<USER>/source/repos/contactRanking/app/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-DCMAKE_FIND_ROOT_PATH=C:/Users/<USER>/source/repos/contactRanking/app/node_modules/react-native-screens/android/.cxx/Debug/$HASH/prefab/$ABI/prefab\n-BC:/Users/<USER>/source/repos/contactRanking/app/node_modules/react-native-screens/android/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DANDROID_STL=c++_shared\n-DRNS_NEW_ARCH_ENABLED=false\n-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "configurationArguments": ["-HC:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=armeabi-v7a", "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a", "-DANDROID_NDK=F:\\Android\\Sdk\\ndk\\27.1.12297006", "-DCMAKE_ANDROID_NDK=F:\\Android\\Sdk\\ndk\\27.1.12297006", "-DCMAKE_TOOLCHAIN_FILE=F:\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\4z91dn29\\obj\\armeabi-v7a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\4z91dn29\\obj\\armeabi-v7a", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_FIND_ROOT_PATH=C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z91dn29\\prefab\\armeabi-v7a\\prefab", "-BC:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\4z91dn29\\armeabi-v7a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared", "-DRNS_NEW_ARCH_ENABLED=false", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"], "stlLibraryFile": "F:\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "intermediatesParentFolder": "C:\\Users\\<USER>\\source\\repos\\contactRanking\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\4z91dn29"}