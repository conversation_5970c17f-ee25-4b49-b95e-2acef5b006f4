package com.callerrep;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

/**
 * Native overlay manager for displaying call quality information
 */
public class CallQualityOverlayManager {
    private static final String TAG = "CallQualityOverlay";
    private static CallQualityOverlayManager instance;
    
    private Context context;
    private WindowManager windowManager;
    private View overlayView;
    private boolean isOverlayShowing = false;
    
    private CallQualityOverlayManager(Context context) {
        this.context = context.getApplicationContext();
        this.windowManager = (WindowManager) this.context.getSystemService(Context.WINDOW_SERVICE);
    }
    
    public static synchronized CallQualityOverlayManager getInstance(Context context) {
        if (instance == null) {
            instance = new CallQualityOverlayManager(context);
        }
        return instance;
    }
    
    /**
     * Check if overlay permission is granted
     */
    public boolean hasOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return Settings.canDrawOverlays(context);
        }
        return true;
    }
    
    /**
     * Show call quality overlay with phone number and quality score
     */
    public void showOverlay(String phoneNumber, int qualityScore, String riskLevel) {
        try {
            if (!hasOverlayPermission()) {
                Log.w(TAG, "📱 No overlay permission - cannot show overlay");
                return;
            }
            
            if (isOverlayShowing) {
                Log.d(TAG, "📱 Overlay already showing, updating content");
                updateOverlayContent(phoneNumber, qualityScore, riskLevel);
                return;
            }
            
            createOverlayView(phoneNumber, qualityScore, riskLevel);
            
            WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
                    ? WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                    : WindowManager.LayoutParams.TYPE_SYSTEM_ALERT,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON |
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
                PixelFormat.TRANSLUCENT
            );

            params.gravity = Gravity.CENTER; // Center both horizontally and vertically
            
            windowManager.addView(overlayView, params);
            isOverlayShowing = true;
            
            Log.d(TAG, "📱 Call quality overlay shown for " + phoneNumber);
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Error showing overlay", e);
        }
    }
    
    /**
     * Update overlay with new quality score (public method for external use)
     */
    public void updateOverlay(String phoneNumber, int qualityScore, String riskLevel) {
        if (isOverlayShowing) {
            Log.d(TAG, "📱 Updating overlay with quality score: " + qualityScore + ", risk: " + riskLevel);
            updateOverlayContent(phoneNumber, qualityScore, riskLevel);
        } else {
            Log.w(TAG, "📱 Cannot update overlay - not currently showing");
        }
    }

    /**
     * Dismiss the overlay
     */
    public void dismissOverlay() {
        try {
            if (isOverlayShowing && overlayView != null) {
                windowManager.removeView(overlayView);
                isOverlayShowing = false;
                overlayView = null;
                Log.d(TAG, "📱 Call quality overlay dismissed");
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ Error dismissing overlay", e);
        }
    }
    
    /**
     * Create the overlay view with call quality information - matching React overlay design
     */
    private void createOverlayView(String phoneNumber, int qualityScore, String riskLevel) {
        // Create outer container with dark background overlay (matching React overlay)
        LinearLayout outerContainer = new LinearLayout(context);
        outerContainer.setOrientation(LinearLayout.VERTICAL);
        outerContainer.setBackgroundColor(Color.parseColor("#B3000000")); // rgba(0, 0, 0, 0.7) equivalent
        outerContainer.setPadding(dpToPx(20), dpToPx(100), dpToPx(20), 0); // Match React overlay margins and top padding

        // Create main container with white background (matching React overlay)
        LinearLayout container = new LinearLayout(context);
        container.setOrientation(LinearLayout.VERTICAL);
        container.setBackgroundColor(Color.WHITE); // White background like React overlay

        // Set rounded corners and shadow (matching React overlay)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            android.graphics.drawable.GradientDrawable shape = new android.graphics.drawable.GradientDrawable();
            shape.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
            shape.setColor(Color.WHITE);
            shape.setCornerRadius(dpToPx(12)); // 12dp radius like React overlay
            container.setBackground(shape);
            container.setElevation(dpToPx(10)); // Shadow elevation
        }

        // Create header section (matching React overlay)
        LinearLayout header = new LinearLayout(context);
        header.setOrientation(LinearLayout.HORIZONTAL);
        header.setPadding(dpToPx(16), dpToPx(16), dpToPx(16), dpToPx(16));

        // Add bottom border to header
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            android.graphics.drawable.GradientDrawable headerBg = new android.graphics.drawable.GradientDrawable();
            headerBg.setColor(Color.WHITE);
            headerBg.setStroke(dpToPx(1), Color.parseColor("#EEEEEE"), 0, 0);
            header.setBackground(headerBg);
        }

        // Header title (matching React overlay)
        TextView headerTitle = new TextView(context);
        headerTitle.setText("Incoming Call Quality");
        headerTitle.setTextColor(Color.parseColor("#333333"));
        headerTitle.setTextSize(18);
        headerTitle.setTypeface(null, android.graphics.Typeface.BOLD);

        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
            0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        header.addView(headerTitle, titleParams);

        // Close button (matching React overlay)
        Button closeButton = new Button(context);
        closeButton.setText("×");
        closeButton.setTextColor(Color.parseColor("#666666"));
        closeButton.setTextSize(24);
        closeButton.setBackgroundColor(Color.TRANSPARENT);
        closeButton.setPadding(0, 0, 0, 0);
        closeButton.setOnClickListener(v -> dismissOverlay());

        LinearLayout.LayoutParams closeParams = new LinearLayout.LayoutParams(
            dpToPx(30), dpToPx(30));
        header.addView(closeButton, closeParams);

        container.addView(header);

        // Create content section (matching React overlay)
        LinearLayout content = new LinearLayout(context);
        content.setOrientation(LinearLayout.VERTICAL);
        content.setPadding(dpToPx(20), dpToPx(20), dpToPx(20), dpToPx(20));

        // Phone number (matching React overlay)
        TextView phoneNumberText = new TextView(context);
        phoneNumberText.setText(phoneNumber != null ? phoneNumber : "Unknown");
        phoneNumberText.setTextColor(Color.parseColor("#333333"));
        phoneNumberText.setTextSize(24);
        phoneNumberText.setTypeface(null, android.graphics.Typeface.BOLD);
        phoneNumberText.setGravity(Gravity.CENTER);
        LinearLayout.LayoutParams phoneParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        phoneParams.bottomMargin = dpToPx(10);
        content.addView(phoneNumberText, phoneParams);

        // Quality section with badge and info (matching React overlay)
        LinearLayout qualitySection = new LinearLayout(context);
        qualitySection.setOrientation(LinearLayout.HORIZONTAL);
        LinearLayout.LayoutParams qualitySectionParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        qualitySectionParams.bottomMargin = dpToPx(20);

        // Quality badge (circular, matching React overlay)
        TextView qualityBadge = new TextView(context);
        qualityBadge.setText(String.valueOf(qualityScore));
        qualityBadge.setTextColor(Color.WHITE);
        qualityBadge.setTextSize(20);
        qualityBadge.setTypeface(null, android.graphics.Typeface.BOLD);
        qualityBadge.setGravity(Gravity.CENTER);

        // Create circular background for badge
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            android.graphics.drawable.GradientDrawable badgeShape = new android.graphics.drawable.GradientDrawable();
            badgeShape.setShape(android.graphics.drawable.GradientDrawable.OVAL);
            badgeShape.setColor(getScoreColor(qualityScore));
            qualityBadge.setBackground(badgeShape);
        } else {
            qualityBadge.setBackgroundColor(getScoreColor(qualityScore));
        }

        LinearLayout.LayoutParams badgeParams = new LinearLayout.LayoutParams(
            dpToPx(60), dpToPx(60));
        badgeParams.rightMargin = dpToPx(15);
        qualitySection.addView(qualityBadge, badgeParams);

        // Quality info section
        LinearLayout qualityInfo = new LinearLayout(context);
        qualityInfo.setOrientation(LinearLayout.VERTICAL);

        // Quality label
        TextView qualityLabel = new TextView(context);
        qualityLabel.setText(getQualityText(qualityScore));
        qualityLabel.setTextColor(Color.parseColor("#333333"));
        qualityLabel.setTextSize(18);
        qualityLabel.setTypeface(null, android.graphics.Typeface.NORMAL);
        LinearLayout.LayoutParams labelParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        labelParams.bottomMargin = dpToPx(4);
        qualityInfo.addView(qualityLabel, labelParams);

        // Risk level
        TextView riskLevelText = new TextView(context);
        riskLevelText.setText(getRiskDisplayText(riskLevel));
        riskLevelText.setTextColor(getRiskColor(riskLevel));
        riskLevelText.setTextSize(16);
        riskLevelText.setTypeface(null, android.graphics.Typeface.NORMAL);
        LinearLayout.LayoutParams riskParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        riskParams.bottomMargin = dpToPx(4);
        qualityInfo.addView(riskLevelText, riskParams);

        LinearLayout.LayoutParams infoParams = new LinearLayout.LayoutParams(
            0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        qualitySection.addView(qualityInfo, infoParams);

        content.addView(qualitySection, qualitySectionParams);

        // Action buttons section (matching React overlay)
        LinearLayout actions = new LinearLayout(context);
        actions.setOrientation(LinearLayout.HORIZONTAL);

        // Report button
        Button reportButton = createActionButton("Report", "#F44336");
        reportButton.setOnClickListener(v -> {
            // TODO: Implement report functionality
            dismissOverlay();
        });

        LinearLayout.LayoutParams reportParams = new LinearLayout.LayoutParams(
            0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        reportParams.rightMargin = dpToPx(5);
        actions.addView(reportButton, reportParams);

        // Dismiss button
        Button dismissButton = createActionButton("Dismiss", "#2196F3");
        dismissButton.setOnClickListener(v -> dismissOverlay());

        LinearLayout.LayoutParams dismissParams = new LinearLayout.LayoutParams(
            0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f);
        dismissParams.leftMargin = dpToPx(5);
        actions.addView(dismissButton, dismissParams);

        content.addView(actions);
        container.addView(content);

        // Add container to outer container
        outerContainer.addView(container);

        overlayView = outerContainer;
    }
    
    /**
     * Update overlay content without recreating the view (matching new layout structure)
     */
    private void updateOverlayContent(String phoneNumber, int qualityScore, String riskLevel) {
        if (overlayView instanceof LinearLayout) {
            LinearLayout outerContainer = (LinearLayout) overlayView;

            // Get the main container (first child of outer container)
            if (outerContainer.getChildCount() > 0 && outerContainer.getChildAt(0) instanceof LinearLayout) {
                LinearLayout mainContainer = (LinearLayout) outerContainer.getChildAt(0);

                // Get the content section (second child of main container, after header)
                if (mainContainer.getChildCount() > 1 && mainContainer.getChildAt(1) instanceof LinearLayout) {
                    LinearLayout content = (LinearLayout) mainContainer.getChildAt(1);

                    // Update phone number (first child of content)
                    if (content.getChildCount() > 0 && content.getChildAt(0) instanceof TextView) {
                        TextView phoneText = (TextView) content.getChildAt(0);
                        phoneText.setText(phoneNumber != null ? phoneNumber : "Unknown");
                    }

                    // Update quality section (second child of content)
                    if (content.getChildCount() > 1 && content.getChildAt(1) instanceof LinearLayout) {
                        LinearLayout qualitySection = (LinearLayout) content.getChildAt(1);

                        // Update quality badge (first child of quality section)
                        if (qualitySection.getChildCount() > 0 && qualitySection.getChildAt(0) instanceof TextView) {
                            TextView badgeText = (TextView) qualitySection.getChildAt(0);
                            badgeText.setText(String.valueOf(qualityScore));

                            // Update badge background color
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                android.graphics.drawable.GradientDrawable badgeShape = new android.graphics.drawable.GradientDrawable();
                                badgeShape.setShape(android.graphics.drawable.GradientDrawable.OVAL);
                                badgeShape.setColor(getScoreColor(qualityScore));
                                badgeText.setBackground(badgeShape);
                            } else {
                                badgeText.setBackgroundColor(getScoreColor(qualityScore));
                            }
                        }

                        // Update quality info (second child of quality section)
                        if (qualitySection.getChildCount() > 1 && qualitySection.getChildAt(1) instanceof LinearLayout) {
                            LinearLayout qualityInfo = (LinearLayout) qualitySection.getChildAt(1);

                            // Update quality label (first child of quality info)
                            if (qualityInfo.getChildCount() > 0 && qualityInfo.getChildAt(0) instanceof TextView) {
                                TextView qualityLabel = (TextView) qualityInfo.getChildAt(0);
                                qualityLabel.setText(getQualityText(qualityScore));
                            }

                            // Update risk level (second child of quality info)
                            if (qualityInfo.getChildCount() > 1 && qualityInfo.getChildAt(1) instanceof TextView) {
                                TextView riskText = (TextView) qualityInfo.getChildAt(1);
                                riskText.setText(getRiskDisplayText(riskLevel));
                                riskText.setTextColor(getRiskColor(riskLevel));
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Get color based on quality score
     */
    private int getScoreColor(int score) {
        if (score >= 80) return Color.parseColor("#4CAF50"); // Material green
        if (score >= 60) return Color.parseColor("#8BC34A"); // Light green
        if (score >= 40) return Color.parseColor("#FF9800"); // Material orange
        return Color.parseColor("#F44336"); // Material red
    }
    
    /**
     * Get color based on risk level
     */
    private int getRiskColor(String riskLevel) {
        if (riskLevel == null) return Color.parseColor("#666666");
        switch (riskLevel.toLowerCase()) {
            case "low": return Color.parseColor("#4CAF50"); // Material green
            case "medium": return Color.parseColor("#FF9800"); // Material orange
            case "high": return Color.parseColor("#F44336"); // Material red
            default: return Color.parseColor("#666666"); // Gray
        }
    }

    /**
     * Get display text for risk level (matching React overlay)
     */
    private String getRiskDisplayText(String riskLevel) {
        if (riskLevel == null) return "Unknown";
        switch (riskLevel.toLowerCase()) {
            case "low": return "Trusted";
            case "medium": return "Caution";
            case "high": return "High Risk";
            default: return "Unknown";
        }
    }

    /**
     * Get quality text based on score (matching React overlay)
     */
    private String getQualityText(int qualityScore) {
        if (qualityScore >= 80) return "Excellent Quality";
        if (qualityScore >= 60) return "Good Quality";
        if (qualityScore >= 40) return "Fair Quality";
        if (qualityScore >= 20) return "Poor Quality";
        return "Very Poor Quality";
    }

    /**
     * Convert dp to pixels
     */
    private int dpToPx(int dp) {
        float density = context.getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    /**
     * Create action button with consistent styling (matching React overlay)
     */
    private Button createActionButton(String text, String colorHex) {
        Button button = new Button(context);
        button.setText(text);
        button.setTextColor(Color.WHITE);
        button.setTextSize(16);
        button.setTypeface(null, android.graphics.Typeface.NORMAL);
        button.setPadding(0, dpToPx(12), 0, dpToPx(12));

        // Create rounded button background
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            android.graphics.drawable.GradientDrawable buttonShape = new android.graphics.drawable.GradientDrawable();
            buttonShape.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
            buttonShape.setColor(Color.parseColor(colorHex));
            buttonShape.setCornerRadius(dpToPx(8));
            button.setBackground(buttonShape);
        } else {
            button.setBackgroundColor(Color.parseColor(colorHex));
        }

        return button;
    }
}
