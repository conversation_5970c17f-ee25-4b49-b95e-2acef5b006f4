import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { StatisticsManager } from '../services/StatisticsManager';
import { CallDetectionService, IncomingCallInfo } from '../services/CallDetectionService';
import { ContactManager } from '../services/ContactManager';

interface CallQualityOverlayProps {
  visible: boolean;
  onClose: () => void;
  callInfo: IncomingCallInfo | null;
}

export const CallQualityOverlay: React.FC<CallQualityOverlayProps> = ({
  visible,
  onClose,
  callInfo: propCallInfo,
}) => {
  const [fadeAnim] = useState(new Animated.Value(0));

  // Safely handle callInfo with explicit null check
  const callInfo = propCallInfo || null;

  useEffect(() => {
    if (visible) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Track overlay shown statistic
      const trackOverlay = async () => {
        try {
          const statsManager = StatisticsManager.getInstance();
          await statsManager.incrementOverlaysShown();
        } catch (error) {
          console.error('Error tracking overlay statistic:', error);
        }
      };
      trackOverlay();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, fadeAnim]);

  const getRiskColor = (riskLevel?: string): string => {
    try {
      switch (String(riskLevel || '')) {
        case 'low':
          return '#4CAF50'; // Green
        case 'medium':
          return '#FF9800'; // Orange
        case 'high':
          return '#F44336'; // Red
        default:
          return '#9E9E9E'; // Gray
      }
    } catch (error) {
      console.error('Error in getRiskColor:', error);
      return '#9E9E9E'; // Gray fallback
    }
  };

  const getRiskText = (riskLevel?: string): string => {
    try {
      switch (String(riskLevel || '')) {
        case 'low':
          return 'Trusted';
        case 'medium':
          return 'Caution';
        case 'high':
          return 'High Risk';
        default:
          return 'Unknown';
      }
    } catch (error) {
      console.error('Error in getRiskText:', error);
      return 'Unknown';
    }
  };

  const getQualityText = (score?: number): string => {
    try {
      if (!score || typeof score !== 'number' || isNaN(score)) return 'No data';
      if (score >= 80) return 'High Quality';
      if (score >= 60) return 'Good Quality';
      if (score >= 40) return 'Low Quality';
      return 'Poor Quality';
    } catch (error) {
      console.error('Error in getQualityText:', error);
      return 'No data';
    }
  };

  const handleReportSpam = async () => {
    if (callInfo && callInfo.phoneNumber) {
      const success = await ContactManager.reportSpam(String(callInfo.phoneNumber), 'spam');
      if (success) {
        console.log('Successfully reported spam');
        // Could show a toast notification
      }
    }
  };

  // Early return with explicit checks
  if (visible !== true) {
    return null;
  }

  if (!callInfo) {
    return null;
  }

  if (!callInfo.phoneNumber || typeof callInfo.phoneNumber !== 'string') {
    return null;
  }

  // Additional safety check to ensure all required properties exist
  const safeCallInfo = {
    phoneNumber: String(callInfo.phoneNumber || 'Unknown'),
    isKnownContact: Boolean(callInfo.isKnownContact),
    qualityScore: typeof callInfo.qualityScore === 'number' ? callInfo.qualityScore : undefined,
    riskLevel: String(callInfo.riskLevel || 'unknown'),
    confidence: typeof callInfo.confidence === 'number' ? callInfo.confidence : undefined,
  };

  const riskColor = getRiskColor(safeCallInfo.riskLevel);

  try {
    return (
      <Modal
        transparent
        visible={visible}
        animationType="none"
        onRequestClose={onClose}
      >
        <View style={styles.overlay}>
          <Animated.View
            style={[
              styles.container,
              {
                opacity: fadeAnim,
                transform: [
                  {
                    translateY: fadeAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [-50, 0],
                    }),
                  },
                ],
              },
            ]}
          >
          <View style={styles.header}>
            <Text style={styles.title}>Incoming Call Quality</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeText}>×</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <Text style={styles.phoneNumber}>
              {String(safeCallInfo.phoneNumber || 'Unknown')}
            </Text>

            {safeCallInfo.isKnownContact === true ? (
              <Text style={styles.knownContact}>📞 Known Contact</Text>
            ) : null}

            <View style={styles.qualitySection}>
              <View style={[styles.qualityBadge, { backgroundColor: riskColor }]}>
                <Text style={styles.qualityScore}>
                  {safeCallInfo.qualityScore && typeof safeCallInfo.qualityScore === 'number'
                    ? String(Math.round(safeCallInfo.qualityScore))
                    : '?'}
                </Text>
              </View>

              <View style={styles.qualityInfo}>
                <Text style={styles.qualityLabel}>
                  {String(getQualityText(safeCallInfo.qualityScore) || 'No data')}
                </Text>
                <Text style={[styles.riskLevel, { color: riskColor }]}>
                  {String(getRiskText(safeCallInfo.riskLevel) || 'Unknown')}
                </Text>
                {safeCallInfo.confidence ? (
                  <Text style={styles.confidence}>
                    {`Confidence: ${String(Math.round(safeCallInfo.confidence))}%`}
                  </Text>
                ) : null}
              </View>
            </View>

            <View style={styles.actions}>
              <TouchableOpacity
                style={[styles.actionButton, styles.reportButton]}
                onPress={handleReportSpam}
              >
                <Text style={styles.actionButtonText}>Report Spam</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.actionButton, styles.dismissButton]}
                onPress={onClose}
              >
                <Text style={styles.actionButtonText}>Dismiss</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
        </View>
      </Modal>
    );
  } catch (error) {
    console.error('Error rendering CallQualityOverlay:', error);
    return (
      <Modal
        transparent
        visible={visible}
        animationType="none"
        onRequestClose={onClose}
      >
        <View style={styles.overlay}>
          <View style={styles.container}>
            <View style={styles.header}>
              <Text style={styles.title}>Call Quality</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Text style={styles.closeText}>×</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.content}>
              <Text style={styles.phoneNumber}>Error loading call info</Text>
              <TouchableOpacity
                style={[styles.actionButton, styles.dismissButton]}
                onPress={onClose}
              >
                <Text style={styles.actionButtonText}>Dismiss</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  }
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-start',
    paddingTop: 100,
  },
  container: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 12,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    fontSize: 24,
    color: '#666',
  },
  content: {
    padding: 20,
  },
  phoneNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  knownContact: {
    fontSize: 16,
    textAlign: 'center',
    color: '#4CAF50',
    marginBottom: 20,
  },
  qualitySection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  qualityBadge: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  qualityScore: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  qualityInfo: {
    flex: 1,
  },
  qualityLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  riskLevel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  confidence: {
    fontSize: 14,
    color: '#666',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 5,
  },
  reportButton: {
    backgroundColor: '#F44336',
  },
  dismissButton: {
    backgroundColor: '#2196F3',
  },
  actionButtonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 16,
  },
});
