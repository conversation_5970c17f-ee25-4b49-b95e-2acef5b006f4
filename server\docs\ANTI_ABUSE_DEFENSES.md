# Anti-Abuse Defenses for Spam Reporting

## Overview

The ContactQuality system implements multiple layers of defense against false spam reports to prevent malicious actors from damaging legitimate businesses or individuals.

## Current Defenses

### 1. **Rate Limiting**
- **Per-device limits**: 50 reports per minute, 20 reports per day
- **IP-based fallback**: If device ID unavailable, uses IP address
- **Configurable thresholds**: Adjustable via environment variables

### 2. **Duplicate Prevention**
- **One report per device per number**: Prevents multiple reports from same device
- **Database constraint**: Enforced at application and database level
- **409 Conflict response**: Clear error message for duplicate attempts

### 3. **Confidence-Weighted Scoring**
- **Variable impact**: Reports with higher confidence have more impact
- **Range validation**: Confidence must be between 0.1 and 1.0
- **Weighted penalties**: Each report contributes `confidence × 5` points (max 50 total)

### 4. **Device Trust Scoring**
- **Age-based trust**: Older devices (30+ days) have higher trust
- **Activity-based trust**: More submissions = higher trust
- **New device penalty**: New devices have 50% impact until established
- **Trust calculation**: `(age_trust × 0.7) + (submission_trust × 0.3)`

### 5. **Capped Impact**
- **Maximum penalty**: 50 points total regardless of report count
- **Minimum score**: Quality scores cannot go below 0
- **Threshold-based risk**: Automatic "high risk" at 10+ reports

### 6. **Suspicious Pattern Detection**
- **Mass reporting detection**: Flags single devices with 5+ reports
- **Rapid succession detection**: Flags reports within 1 minute
- **Bot detection**: Flags identical confidence levels across reports
- **Automatic logging**: Suspicious patterns logged for review

### 7. **Device Fingerprinting**
- **Hardware identification**: Uses device-specific identifiers
- **Submission tracking**: Tracks total submissions and first submission date
- **Cross-reference validation**: Links reports to established device history

## Configuration

All anti-abuse settings are configurable via `server/src/config/antiAbuse.ts`:

```typescript
export const defaultAntiAbuseConfig = {
  maxReportsPerDay: 20,
  maxReportsPerHour: 5,
  preventDuplicateReports: true,
  minConfidenceLevel: 0.1,
  maxConfidenceLevel: 1.0,
  maxSpamPenalty: 50,
  baseReportPenalty: 5,
  enableDeviceTrustScoring: true,
  newDevicePenaltyMultiplier: 0.5,
  trustedDeviceThreshold: 30, // days
  autoReviewThreshold: 15,
  suspiciousPatternDetection: true,
};
```

## Database Schema

### Spam Reports Table
```sql
CREATE TABLE spam_reports (
  id SERIAL PRIMARY KEY,
  fingerprint_hash VARCHAR(64) NOT NULL,
  device_id VARCHAR(100) NOT NULL,
  report_type report_type_enum NOT NULL,
  confidence DECIMAL(3,2) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  
  -- Indexes for performance
  INDEX(fingerprint_hash),
  INDEX(device_id),
  INDEX(created_at)
);
```

### Device Tracking
```sql
CREATE TABLE devices (
  id SERIAL PRIMARY KEY,
  device_id VARCHAR(100) UNIQUE NOT NULL,
  first_submission TIMESTAMP NOT NULL,
  last_submission TIMESTAMP NOT NULL,
  total_submissions INTEGER DEFAULT 0,
  app_version VARCHAR(20),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## API Responses

### Duplicate Report (409)
```json
{
  "success": false,
  "message": "You have already reported this number"
}
```

### Rate Limit Exceeded (429)
```json
{
  "success": false,
  "message": "Daily report limit exceeded. Please try again tomorrow."
}
```

### Successful Report (200)
```json
{
  "success": true,
  "message": "Spam report submitted successfully"
}
```

## Monitoring & Alerts

### Suspicious Activity Logging
- **Pattern detection**: Automatic logging of suspicious patterns
- **Device analysis**: Tracking of high-volume reporting devices
- **Fingerprint analysis**: Monitoring numbers with unusual report patterns

### Metrics to Monitor
1. **Report velocity**: Reports per hour/day across system
2. **Device distribution**: Concentration of reports from few devices
3. **Confidence patterns**: Unusual confidence level distributions
4. **Geographic clustering**: Reports from same IP ranges
5. **Timing patterns**: Reports submitted in rapid succession

## Future Enhancements

### 1. **Machine Learning Detection**
- Train models on known false report patterns
- Behavioral analysis of reporting devices
- Anomaly detection for unusual reporting patterns

### 2. **Community Validation**
- Cross-reference with other spam databases
- Peer review system for disputed reports
- Reputation scoring for reporting devices

### 3. **Business Protection**
- Whitelist for verified business numbers
- Appeal process for false reports
- Enhanced verification for business accounts

### 4. **Advanced Rate Limiting**
- Dynamic rate limits based on device trust
- Exponential backoff for suspicious devices
- Geographic rate limiting

## Testing Anti-Abuse Measures

### Unit Tests
- Confidence weighting calculations
- Device trust score calculations
- Suspicious pattern detection

### Integration Tests
- Rate limiting enforcement
- Duplicate prevention
- End-to-end spam reporting flow

### Load Tests
- High-volume reporting scenarios
- Concurrent report submissions
- Database performance under load

## Incident Response

### False Report Detection
1. **Immediate**: Suspend high-volume reporting devices
2. **Investigation**: Analyze reporting patterns and device history
3. **Remediation**: Adjust quality scores if false reports confirmed
4. **Prevention**: Update detection algorithms based on findings

### System Abuse
1. **Detection**: Monitor for coordinated attack patterns
2. **Mitigation**: Implement emergency rate limits
3. **Analysis**: Identify attack vectors and vulnerabilities
4. **Hardening**: Deploy additional protections based on attack analysis
