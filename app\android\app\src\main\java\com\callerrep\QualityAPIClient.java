package com.callerrep;

import android.os.AsyncTask;
import android.util.Log;
import org.json.JSONObject;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;

/**
 * Native Android HTTP client for fetching call quality scores from the server
 */
public class QualityAPIClient {
    private static final String TAG = "QualityAPIClient";
    private static final String BASE_URL = "http://99.9.199.51:3001"; // Production server
    private static final String API_VERSION = "api/v1";
    private static final int TIMEOUT = 10000; // 10 seconds
    private static final String API_KEY = "callerrep-mobile-app-v1";

    /**
     * Interface for handling API responses
     */
    public interface QualityScoreCallback {
        void onSuccess(int qualityScore, String riskLevel);
        void onError(String error);
    }

    /**
     * Interface for handling spam report responses
     */
    public interface SpamReportCallback {
        void onSuccess(String message);
        void onError(String error);
    }

    /**
     * Get quality score for a phone number (async)
     */
    public static void getQualityScore(String phoneNumber, QualityScoreCallback callback) {
        new GetQualityScoreTask(callback).execute(phoneNumber);
    }

    /**
     * Report a phone number as spam (async)
     */
    public static void reportSpam(String phoneNumber, String reportType, SpamReportCallback callback) {
        Log.d(TAG, "🚨 Reporting spam for: " + phoneNumber + " as " + reportType);
        new ReportSpamTask(callback).execute(phoneNumber, reportType);
    }

    /**
     * Create fingerprint for phone number (same logic as React Native)
     */
    private static String createFingerprint(String phoneNumber) {
        try {
            // Normalize phone number (remove non-digits)
            String normalized = phoneNumber.replaceAll("[^0-9]", "");
            
            // Create SHA-256 hash
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(normalized.getBytes(StandardCharsets.UTF_8));
            
            // Convert to hex string
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (Exception e) {
            Log.e(TAG, "Error creating fingerprint", e);
            return null;
        }
    }

    /**
     * AsyncTask to fetch quality score from server
     */
    private static class GetQualityScoreTask extends AsyncTask<String, Void, QualityResult> {
        private final QualityScoreCallback callback;

        public GetQualityScoreTask(QualityScoreCallback callback) {
            this.callback = callback;
        }

        @Override
        protected QualityResult doInBackground(String... phoneNumbers) {
            if (phoneNumbers.length == 0) {
                return new QualityResult(false, "No phone number provided", 50, "unknown");
            }

            String phoneNumber = phoneNumbers[0];
            String fingerprint = createFingerprint(phoneNumber);
            
            if (fingerprint == null) {
                return new QualityResult(false, "Failed to create fingerprint", 50, "unknown");
            }

            try {
                String urlString = BASE_URL + "/" + API_VERSION + "/quality/" + fingerprint;
                URL url = new URL(urlString);
                
                Log.d(TAG, "🌐 Fetching quality score from: " + urlString);
                
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(TIMEOUT);
                connection.setReadTimeout(TIMEOUT);
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("User-Agent", "CallerRep/1.0");
                connection.setRequestProperty("X-API-Key", API_KEY);

                int responseCode = connection.getResponseCode();
                Log.d(TAG, "📡 Server response code: " + responseCode);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    String jsonResponse = response.toString();
                    Log.d(TAG, "📊 Server response: " + jsonResponse);
                    
                    // Parse JSON response
                    JSONObject jsonObject = new JSONObject(jsonResponse);
                    int qualityScore = jsonObject.optInt("qualityScore", 50);
                    String riskLevel = jsonObject.optString("riskLevel", "unknown");
                    
                    return new QualityResult(true, "Success", qualityScore, riskLevel);
                    
                } else if (responseCode == HttpURLConnection.HTTP_NOT_FOUND) {
                    // Number not found in database - return default values
                    Log.d(TAG, "📊 Number not found in database, using defaults");
                    return new QualityResult(true, "Not found", 50, "unknown");
                    
                } else {
                    String error = "HTTP " + responseCode + ": " + connection.getResponseMessage();
                    Log.e(TAG, "❌ Server error: " + error);
                    return new QualityResult(false, error, 50, "unknown");
                }
                
            } catch (Exception e) {
                String error = "Network error: " + e.getMessage();
                Log.e(TAG, "❌ Network error fetching quality score", e);
                return new QualityResult(false, error, 50, "unknown");
            }
        }

        @Override
        protected void onPostExecute(QualityResult result) {
            if (callback != null) {
                if (result.success) {
                    callback.onSuccess(result.qualityScore, result.riskLevel);
                } else {
                    callback.onError(result.error);
                }
            }
        }
    }

    /**
     * AsyncTask to report spam to server
     */
    private static class ReportSpamTask extends AsyncTask<String, Void, SpamReportResult> {
        private final SpamReportCallback callback;

        public ReportSpamTask(SpamReportCallback callback) {
            this.callback = callback;
        }

        @Override
        protected SpamReportResult doInBackground(String... params) {
            if (params.length < 2) {
                return new SpamReportResult(false, "Missing phone number or report type");
            }

            String phoneNumber = params[0];
            String reportType = params[1];
            String fingerprint = createFingerprint(phoneNumber);

            if (fingerprint == null) {
                return new SpamReportResult(false, "Failed to create fingerprint");
            }

            try {
                // Create device ID (simplified for now)
                String deviceId = "android-native-" + System.currentTimeMillis();

                URL url = new URL(BASE_URL + "/" + API_VERSION + "/fingerprint/report-spam");
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();

                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("X-API-Key", API_KEY);
                connection.setDoOutput(true);
                connection.setConnectTimeout(TIMEOUT);
                connection.setReadTimeout(TIMEOUT);

                // Create JSON payload
                JSONObject payload = new JSONObject();
                payload.put("fingerprint", fingerprint);
                payload.put("deviceId", deviceId);
                payload.put("reportType", reportType);
                payload.put("confidence", 0.8);
                payload.put("description", "User reported as " + reportType + " from native overlay");

                // Send request
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = payload.toString().getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }

                int responseCode = connection.getResponseCode();
                Log.d(TAG, "🚨 Spam report response code: " + responseCode);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;

                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    String jsonResponse = response.toString();
                    Log.d(TAG, "🚨 Spam report response: " + jsonResponse);

                    JSONObject jsonObject = new JSONObject(jsonResponse);
                    boolean success = jsonObject.optBoolean("success", false);
                    String message = jsonObject.optString("message", "Report submitted");

                    return new SpamReportResult(success, success ? message : "Server reported failure");
                } else {
                    String error = "HTTP " + responseCode + ": " + connection.getResponseMessage();
                    Log.e(TAG, "❌ Spam report error: " + error);
                    return new SpamReportResult(false, error);
                }

            } catch (Exception e) {
                String error = "Network error: " + e.getMessage();
                Log.e(TAG, "❌ Network error reporting spam", e);
                return new SpamReportResult(false, error);
            }
        }

        @Override
        protected void onPostExecute(SpamReportResult result) {
            if (result.success) {
                callback.onSuccess(result.message);
            } else {
                callback.onError(result.error);
            }
        }
    }

    /**
     * Result class for spam report requests
     */
    private static class SpamReportResult {
        final boolean success;
        final String error;
        final String message;

        SpamReportResult(boolean success, String errorOrMessage) {
            this.success = success;
            if (success) {
                this.message = errorOrMessage;
                this.error = null;
            } else {
                this.error = errorOrMessage;
                this.message = null;
            }
        }
    }

    /**
     * Result class for quality score requests
     */
    private static class QualityResult {
        final boolean success;
        final String error;
        final int qualityScore;
        final String riskLevel;

        QualityResult(boolean success, String error, int qualityScore, String riskLevel) {
            this.success = success;
            this.error = error;
            this.qualityScore = qualityScore;
            this.riskLevel = riskLevel;
        }
    }
}
