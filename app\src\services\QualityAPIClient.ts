import { FingerprintSubmission, QualityResponse } from '../types/Contact';
import { Platform } from 'react-native';

// Device billing types
export interface DeviceBillingInfo {
  currentMonthRequests: number;
  freeRequestsUsed: number;
  billableRequests: number;
  estimatedBillCents: number;
  hasUnpaidBills: boolean;
  billingMonth: string;
}

export interface DeviceUsageSummary extends DeviceBillingInfo {
  deviceFingerprint: string;
  freeRequestsRemaining: number;
  nextBillingDate: string;
}

export interface DeviceBill {
  id: number;
  deviceFingerprint: string;
  billingMonth: string;
  amountCents: number;
  billableRequests: number;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  dueDate: string;
  paidAt?: string;
  stripeInvoiceId?: string;
  createdAt: string;
}

export interface BillingConfig {
  freeRequestsPerMonth: number;
  pricePerRequestCents: number;
  pricePerRequestDollars: number;
  minimumBillCents: number;
  minimumBillDollars: number;
}

export interface PaymentLinkResponse {
  billId: number;
  paymentUrl: string;
}

export class QualityAPIClient {
  // Platform-specific server URLs for local development
  private static readonly BASE_URL = Platform.OS === 'ios'
    ? 'http://localhost:3001'     // iOS Simulator uses localhost
    : 'http://********:3001';     // Android emulator uses ********
  private static readonly API_VERSION = 'api/v1';
  private static readonly TIMEOUT = 10000; // 10 seconds
  private static readonly API_KEY = 'callerrep-mobile-app-v1'; // Simple API key for development
  // Fixed API endpoints and authentication

  /**
   * Create an AbortController with timeout for React Native compatibility
   */
  private static createTimeoutController(timeoutMs: number): AbortController {
    const controller = new AbortController();
    setTimeout(() => controller.abort(), timeoutMs);
    return controller;
  }

  /**
   * Get device ID for API requests
   */
  private static async getDeviceId(): Promise<string> {
    try {
      // Use FingerprintManager to get device ID
      const { FingerprintManager } = require('./FingerprintManager');
      return await FingerprintManager.getDeviceId();
    } catch (error) {
      console.error('Error getting device ID:', error);
      // Fallback to a generated ID based on device characteristics
      return 'mobile-app-' + Date.now().toString();
    }
  }

  /**
   * Get common headers for API requests
   */
  private static getHeaders(contentType: string = 'application/json'): Record<string, string> {
    return {
      'Content-Type': contentType,
      'User-Agent': 'CallerRep/1.0',
      'X-API-Key': this.API_KEY,
    };
  }

  /**
   * Get device fingerprint headers for billing requests
   */
  private static async getDeviceHeaders(deviceFingerprint: string): Promise<Record<string, string>> {
    return {
      ...this.getHeaders(),
      'X-Device-Fingerprint': deviceFingerprint,
      'X-Device-Platform': 'react-native',
    };
  }

  /**
   * Submit contact fingerprints to the server
   */
  static async submitFingerprints(submission: FingerprintSubmission): Promise<boolean> {
    try {
      const response = await fetch(`${this.BASE_URL}/${this.API_VERSION}/fingerprint/submit`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(submission),
        signal: this.createTimeoutController(this.TIMEOUT).signal,
      });

      if (!response.ok) {
        console.error('Failed to submit fingerprints:', response.status, response.statusText);
        return false;
      }

      const result = await response.json();
      console.log(`Successfully submitted ${submission.fingerprints.length} fingerprints`);
      return result.success === true;
    } catch (error) {
      console.error('Error submitting fingerprints:', error);
      return false;
    }
  }

  /**
   * Get quality score for a single phone number fingerprint
   */
  static async getQualityScore(fingerprint: string): Promise<QualityResponse | null> {
    try {
      const response = await fetch(
        `${this.BASE_URL}/${this.API_VERSION}/quality/${fingerprint}`,
        {
          method: 'GET',
          headers: this.getHeaders(),
          signal: this.createTimeoutController(this.TIMEOUT).signal,
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          // Number not found in database
          return null;
        }
        console.error('Failed to get quality score:', response.status, response.statusText);
        return null;
      }

      const qualityData: QualityResponse = await response.json();
      return qualityData;
    } catch (error) {
      console.error('Error getting quality score:', error);
      return null;
    }
  }

  /**
   * Get quality scores for multiple fingerprints (batch request)
   */
  static async getBatchQualityScores(fingerprints: string[]): Promise<Map<string, QualityResponse>> {
    const results = new Map<string, QualityResponse>();

    try {
      const response = await fetch(`${this.BASE_URL}/${this.API_VERSION}/quality/batch`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ fingerprints }),
        signal: this.createTimeoutController(this.TIMEOUT * 2).signal, // Longer timeout for batch
      });

      if (!response.ok) {
        console.error('Failed to get batch quality scores:', response.status, response.statusText);
        return results;
      }

      const batchData: { [fingerprint: string]: QualityResponse } = await response.json();
      
      for (const [fingerprint, qualityData] of Object.entries(batchData)) {
        results.set(fingerprint, qualityData);
      }

      console.log(`Retrieved quality scores for ${results.size} numbers`);
    } catch (error) {
      console.error('Error getting batch quality scores:', error);
    }

    return results;
  }

  /**
   * Report a number as spam/unwanted
   */
  static async reportSpam(fingerprint: string, reportType: 'spam' | 'scam' | 'robocall'): Promise<boolean> {
    try {
      // Get device ID for the report
      const deviceId = await this.getDeviceId();

      const response = await fetch(`${this.BASE_URL}/${this.API_VERSION}/fingerprint/report-spam`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          fingerprint,
          deviceId,
          reportType,
          confidence: 0.8, // Default confidence level
          description: `User reported as ${reportType} from mobile app`,
        }),
        signal: this.createTimeoutController(this.TIMEOUT).signal,
      });

      if (!response.ok) {
        console.error('Failed to report spam:', response.status, response.statusText);
        return false;
      }

      const result = await response.json();
      console.log(`Successfully reported ${reportType} for fingerprint:`, result.message);
      return result.success === true;
    } catch (error) {
      console.error('Error reporting spam:', error);
      return false;
    }
  }

  /**
   * Get server statistics (optional, for debugging)
   */
  static async getServerStats(): Promise<any> {
    try {
      const response = await fetch(`${this.BASE_URL}/${this.API_VERSION}/analytics/overview`, {
        method: 'GET',
        headers: this.getHeaders(),
        signal: this.createTimeoutController(this.TIMEOUT).signal,
      });

      if (!response.ok) {
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting server stats:', error);
      return null;
    }
  }

  /**
   * Check if the API server is reachable
   */
  static async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.BASE_URL}/health`, {
        method: 'GET',
        signal: this.createTimeoutController(5000).signal, // Short timeout for health check
      });

      return response.ok;
    } catch (error) {
      console.error('API health check failed:', error);
      return false;
    }
  }

  /**
   * Get trending spam numbers (optional feature)
   */
  static async getTrendingSpam(limit: number = 100): Promise<QualityResponse[]> {
    try {
      const response = await fetch(
        `${this.BASE_URL}/${this.API_VERSION}/trending/spam?limit=${limit}`,
        {
          method: 'GET',
          headers: this.getHeaders(),
          signal: this.createTimeoutController(this.TIMEOUT).signal,
        }
      );

      if (!response.ok) {
        return [];
      }

      const data = await response.json();
      return data.trending || [];
    } catch (error) {
      console.error('Error getting trending spam:', error);
      return [];
    }
  }

  /**
   * Submit feedback about quality score accuracy
   */
  static async submitFeedback(
    fingerprint: string,
    actualQuality: 'good' | 'bad' | 'spam',
    predictedScore: number
  ): Promise<boolean> {
    try {
      const response = await fetch(`${this.BASE_URL}/${this.API_VERSION}/feedback`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          fingerprint,
          actualQuality,
          predictedScore,
          timestamp: Date.now(),
        }),
        signal: this.createTimeoutController(this.TIMEOUT).signal,
      });

      return response.ok;
    } catch (error) {
      console.error('Error submitting feedback:', error);
      return false;
    }
  }

  // ===== DEVICE BILLING METHODS =====

  /**
   * Get device usage summary and billing information
   */
  static async getDeviceUsage(deviceFingerprint: string): Promise<DeviceUsageSummary | null> {
    try {
      const response = await fetch(
        `${this.BASE_URL}/${this.API_VERSION}/device-billing/usage/${deviceFingerprint}`,
        {
          method: 'GET',
          headers: await this.getDeviceHeaders(deviceFingerprint),
          signal: this.createTimeoutController(this.TIMEOUT).signal,
        }
      );

      if (!response.ok) {
        console.error('Failed to get device usage:', response.status, response.statusText);
        return null;
      }

      const result = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Error getting device usage:', error);
      return null;
    }
  }

  /**
   * Get billing configuration
   */
  static async getBillingConfig(): Promise<BillingConfig | null> {
    try {
      const response = await fetch(
        `${this.BASE_URL}/${this.API_VERSION}/device-billing/config`,
        {
          method: 'GET',
          headers: this.getHeaders(),
          signal: this.createTimeoutController(this.TIMEOUT).signal,
        }
      );

      if (!response.ok) {
        console.error('Failed to get billing config:', response.status, response.statusText);
        return null;
      }

      const result = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Error getting billing config:', error);
      return null;
    }
  }

  /**
   * Get device billing history
   */
  static async getDeviceBills(deviceFingerprint: string, limit: number = 12): Promise<DeviceBill[]> {
    try {
      const response = await fetch(
        `${this.BASE_URL}/${this.API_VERSION}/device-billing/bills/${deviceFingerprint}?limit=${limit}`,
        {
          method: 'GET',
          headers: await this.getDeviceHeaders(deviceFingerprint),
          signal: this.createTimeoutController(this.TIMEOUT).signal,
        }
      );

      if (!response.ok) {
        console.error('Failed to get device bills:', response.status, response.statusText);
        return [];
      }

      const result = await response.json();
      return result.success ? result.data.bills : [];
    } catch (error) {
      console.error('Error getting device bills:', error);
      return [];
    }
  }

  /**
   * Check if device has access (not blocked)
   */
  static async checkDeviceAccess(deviceFingerprint: string): Promise<{ hasAccess: boolean; isBlocked: boolean; hasUnpaidBills: boolean } | null> {
    try {
      const response = await fetch(
        `${this.BASE_URL}/${this.API_VERSION}/device-billing/check-access`,
        {
          method: 'POST',
          headers: await this.getDeviceHeaders(deviceFingerprint),
          body: JSON.stringify({ deviceFingerprint }),
          signal: this.createTimeoutController(this.TIMEOUT).signal,
        }
      );

      if (!response.ok) {
        console.error('Failed to check device access:', response.status, response.statusText);
        return null;
      }

      const result = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Error checking device access:', error);
      return null;
    }
  }

  /**
   * Create payment link for a bill
   */
  static async createPaymentLink(billId: number): Promise<PaymentLinkResponse | null> {
    try {
      const response = await fetch(
        `${this.BASE_URL}/${this.API_VERSION}/device-billing/create-payment-link/${billId}`,
        {
          method: 'POST',
          headers: this.getHeaders(),
          signal: this.createTimeoutController(this.TIMEOUT).signal,
        }
      );

      if (!response.ok) {
        console.error('Failed to create payment link:', response.status, response.statusText);
        return null;
      }

      const result = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Error creating payment link:', error);
      return null;
    }
  }
}
